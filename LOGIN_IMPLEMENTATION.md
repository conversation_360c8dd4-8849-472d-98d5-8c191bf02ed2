# Login System Implementation

This document describes the implementation of the OTP-based login system for the Sarafi application.

## Overview

The login system implements a two-step authentication process:
1. User enters mobile number → POST `/users/send-otp`
2. User enters OTP code → POST `/users/verify-otp`

## API Endpoints

### Send OTP
- **Endpoint**: `POST /users/send-otp`
- **Request Body**:
  ```json
  {
    "mobileNumber": "09123456789"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "O<PERSON> sent successfully",
    "data": {
      "expiresIn": 120
    }
  }
  ```

### Verify OTP
- **Endpoint**: `POST /users/verify-otp`
- **Request Body**:
  ```json
  {
    "mobileNumber": "09123456789",
    "otp": "123456"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "<PERSON><PERSON> verified successfully",
    "data": {
      "user": {
        "id": "user_id",
        "mobileNumber": "09123456789",
        "isActive": true,
        "lastLoginAt": "2024-01-01T00:00:00Z",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      },
      "accessToken": "jwt_token_here",
      "tokenType": "Bearer"
    }
  }
  ```

## File Structure

### Components
- `src/components/LoginForm.tsx` - Mobile number input form
- `src/components/OTPVerification.tsx` - OTP verification form
- `src/components/LoginForm.module.css` - Login form styles
- `src/components/OTPVerification.module.css` - OTP form styles

### Pages
- `src/app/login/page.tsx` - Main login page
- `src/app/login/verify/page.tsx` - OTP verification page

### Services & Utils
- `src/services/authService.ts` - API calls for authentication
- `src/contexts/AuthContext.tsx` - Global authentication state
- `src/utils/storage.ts` - localStorage management
- `src/utils/validation.ts` - Form validation utilities

### Types
- `src/types/auth.ts` - Authentication-related TypeScript types

## Features

### Login Form
- Iranian mobile number validation (09xxxxxxxxx format)
- Real-time validation feedback
- Loading states during API calls
- Error handling and display
- Responsive design with RTL support

### OTP Verification
- 4-6 digit OTP input with auto-formatting
- 2-minute countdown timer
- Resend OTP functionality
- Back to mobile number option
- Auto-redirect after successful verification

### Authentication State
- Global authentication context using React Context
- Persistent login state using localStorage
- Automatic token management
- User information storage

### Header Integration
- Shows login button when not authenticated
- Displays user mobile number when authenticated
- Logout functionality
- Responsive design

## Usage

### Accessing Login Pages
- Login: `http://localhost:4000/login`
- OTP Verification: `http://localhost:4000/login/verify`

### Authentication Flow
1. User visits `/login`
2. Enters mobile number and clicks "ارسال کد تأیید"
3. System calls `/users/send-otp` API
4. User is redirected to `/login/verify`
5. User enters OTP code and clicks "تأیید و ورود"
6. System calls `/users/verify-otp` API
7. On success, user is logged in and redirected to home page
8. Authentication state is persisted in localStorage

### Logout
- Click "خروج" button in header
- Clears authentication state and localStorage
- Redirects to home page

## Configuration

### API Configuration
Update `src/config/api.ts` to set the correct API base URL:
```typescript
export const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://your-api-url',
  endpoints: {
    sendOtp: '/users/send-otp',
    verifyOtp: '/users/verify-otp',
  },
} as const;
```

### Environment Variables
Set the API base URL in your environment:
```bash
NEXT_PUBLIC_API_BASE_URL=http://your-api-url
```

## Testing

The implementation includes comprehensive tests:
- `src/__tests__/authService.test.ts` - API service tests
- `src/__tests__/validation.test.ts` - Validation utility tests

Run tests with:
```bash
npm test
```

## Security Considerations

- OTP codes are validated on both client and server
- Authentication tokens are stored securely in localStorage
- Mobile numbers are validated using Iranian format
- API errors are handled gracefully without exposing sensitive information
- HTTPS should be used in production for secure token transmission

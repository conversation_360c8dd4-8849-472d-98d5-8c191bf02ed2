/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CurrencyExchange from '@/components/CurrencyExchange';
import { CurrencyService } from '@/services/currencyService';

// Mock the CurrencyService
jest.mock('@/services/currencyService');
const mockCurrencyService = CurrencyService as jest.Mocked<typeof CurrencyService>;

const mockCurrencies = [
  {
    _id: "6842f858fb5c71e0b34fd550",
    name: "toman",
    sellPrice: 1,
    buyPrice: 1,
    fa: "تومان"
  },
  {
    _id: "6842f858fb5c71e0b34fd551",
    name: "lira",
    sellPrice: 2140,
    buyPrice: 2100,
    fa: "لیر"
  },
  {
    _id: "6842f858fb5c71e0b34fd552",
    name: "dollar",
    sellPrice: 42000,
    buyPrice: 41500,
    fa: "دلار"
  }
];

describe('CurrencyExchange Dropdown', () => {
  beforeEach(() => {
    mockCurrencyService.getCurrencies.mockResolvedValue(mockCurrencies);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('dropdown buttons should be clickable and toggle dropdown visibility', async () => {
    render(<CurrencyExchange />);

    // Wait for currencies to load
    await waitFor(() => {
      expect(screen.getByText('تومان (TOMAN)')).toBeInTheDocument();
    });

    // Find dropdown buttons
    const dropdownButtons = screen.getAllByText('▼');
    expect(dropdownButtons).toHaveLength(2);

    // Click the first dropdown button (from currency)
    fireEvent.click(dropdownButtons[0]);

    // Check if dropdown opened (arrow should change to ▲)
    await waitFor(() => {
      expect(screen.getByText('▲')).toBeInTheDocument();
    });

    // Check if dropdown items are visible
    const dropdownItems = screen.getAllByText('لیر (LIRA)');
    expect(dropdownItems.length).toBeGreaterThan(0);
    expect(screen.getByText('دلار (DOLLAR)')).toBeInTheDocument();

    // Click the dropdown button again to close
    fireEvent.click(screen.getByText('▲'));

    // Check if dropdown closed (arrow should change back to ▼)
    await waitFor(() => {
      expect(screen.getAllByText('▼')).toHaveLength(2);
    });
  });

  test('should be able to select a currency from dropdown', async () => {
    render(<CurrencyExchange />);

    // Wait for currencies to load
    await waitFor(() => {
      expect(screen.getByText('تومان (TOMAN)')).toBeInTheDocument();
    });

    // Click the first dropdown button
    const dropdownButtons = screen.getAllByText('▼');
    fireEvent.click(dropdownButtons[0]);

    // Wait for dropdown to open
    await waitFor(() => {
      expect(screen.getByText('▲')).toBeInTheDocument();
    });

    // Click on "لیر" option in the dropdown
    const liraOptions = screen.getAllByText('لیر (LIRA)');
    fireEvent.click(liraOptions[0]); // Click the first one (in dropdown)

    // Check if the currency changed and dropdown closed
    await waitFor(() => {
      const liraElements = screen.getAllByText(/لیر \(LIRA\)/);
      expect(liraElements.length).toBeGreaterThan(0);
      expect(screen.getAllByText('▼')).toHaveLength(2);
    });
  });

  test('clicking outside should close dropdown', async () => {
    render(<CurrencyExchange />);

    // Wait for currencies to load
    await waitFor(() => {
      expect(screen.getByText('تومان (TOMAN)')).toBeInTheDocument();
    });

    // Click the first dropdown button
    const dropdownButtons = screen.getAllByText('▼');
    fireEvent.click(dropdownButtons[0]);

    // Wait for dropdown to open
    await waitFor(() => {
      expect(screen.getByText('▲')).toBeInTheDocument();
    });

    // Click outside the dropdown
    fireEvent.mouseDown(document.body);

    // Check if dropdown closed
    await waitFor(() => {
      expect(screen.getAllByText('▼')).toHaveLength(2);
    });
  });

  test('opening one dropdown should close the other', async () => {
    render(<CurrencyExchange />);

    // Wait for currencies to load
    await waitFor(() => {
      expect(screen.getByText('تومان (TOMAN)')).toBeInTheDocument();
    });

    const dropdownButtons = screen.getAllByText('▼');

    // Open first dropdown
    fireEvent.click(dropdownButtons[0]);
    await waitFor(() => {
      expect(screen.getByText('▲')).toBeInTheDocument();
    });

    // Open second dropdown
    fireEvent.click(dropdownButtons[1]);

    // Check that only one dropdown is open
    await waitFor(() => {
      const upArrows = screen.getAllByText('▲');
      expect(upArrows).toHaveLength(1);
    });
  });
});

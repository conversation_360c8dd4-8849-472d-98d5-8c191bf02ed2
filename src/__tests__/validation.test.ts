import { validateMobileNumber, validateOtp, formatMobileNumber } from '@/utils/validation';

describe('Validation Utils', () => {
  describe('validateMobileNumber', () => {
    it('should validate correct Iranian mobile numbers', () => {
      const validNumbers = [
        '09123456789',
        '09901234567',
        '09351234567',
      ];

      validNumbers.forEach(number => {
        const result = validateMobileNumber(number);
        expect(result.isValid).toBe(true);
        expect(result.error).toBeUndefined();
      });
    });

    it('should reject invalid mobile numbers', () => {
      const invalidNumbers = [
        '',
        '123456789',
        '0912345678', // too short
        '091234567890', // too long
        '08123456789', // wrong prefix
        '9123456789', // missing 0
        'abc123456789', // contains letters
      ];

      invalidNumbers.forEach(number => {
        const result = validateMobileNumber(number);
        expect(result.isValid).toBe(false);
        expect(result.error).toBeDefined();
      });
    });

    it('should handle mobile numbers with spaces and dashes', () => {
      const result = validateMobileNumber('0912 345 6789');
      expect(result.isValid).toBe(true);
      
      const result2 = validateMobileNumber('0912-345-6789');
      expect(result2.isValid).toBe(true);
    });
  });

  describe('validateOtp', () => {
    it('should validate correct OTP codes', () => {
      const validOtps = [
        '1234',
        '123456',
        '000000',
        '999999',
      ];

      validOtps.forEach(otp => {
        const result = validateOtp(otp);
        expect(result.isValid).toBe(true);
        expect(result.error).toBeUndefined();
      });
    });

    it('should reject invalid OTP codes', () => {
      const invalidOtps = [
        '',
        '123', // too short
        '1234567', // too long
        'abcd', // contains letters
        '12 34', // contains spaces (after cleaning should be valid)
      ];

      // Note: '12 34' should be valid after cleaning spaces
      const shouldBeValid = ['12 34'];
      const shouldBeInvalid = invalidOtps.filter(otp => !shouldBeValid.includes(otp));

      shouldBeInvalid.forEach(otp => {
        const result = validateOtp(otp);
        expect(result.isValid).toBe(false);
        expect(result.error).toBeDefined();
      });

      // Test the space case separately
      const spaceResult = validateOtp('12 34');
      expect(spaceResult.isValid).toBe(true);
    });
  });

  describe('formatMobileNumber', () => {
    it('should format 11-digit mobile numbers correctly', () => {
      const result = formatMobileNumber('09123456789');
      expect(result).toBe('0912 345 6789');
    });

    it('should return original string for non-11-digit numbers', () => {
      const shortNumber = '0912345678';
      expect(formatMobileNumber(shortNumber)).toBe(shortNumber);
      
      const longNumber = '091234567890';
      expect(formatMobileNumber(longNumber)).toBe(longNumber);
    });

    it('should handle already formatted numbers', () => {
      const formatted = '0912 345 6789';
      const result = formatMobileNumber(formatted);
      expect(result).toBe('0912 345 6789');
    });
  });
});

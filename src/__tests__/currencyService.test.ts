/**
 * @jest-environment jsdom
 */

import { CurrencyService } from '@/services/currencyService';
import { Currency } from '@/types/currency';

// Mock fetch
global.fetch = jest.fn();

describe('CurrencyService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockCurrencies: Currency[] = [
    {
      _id: "6842f858fb5c71e0b34fd550",
      name: "toman",
      sellPrice: 1,
      buyPrice: 1,
      fa: "تومان"
    },
    {
      _id: "6842f858fb5c71e0b34fd551",
      name: "lira",
      sellPrice: 2140,
      buyPrice: 2100,
      fa: "لیر"
    }
  ];

  describe('getCurrencies', () => {
    it('should fetch currencies successfully', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockCurrencies,
      });

      const result = await CurrencyService.getCurrencies();

      expect(fetch).toHaveBeenCalledWith('/api/currencies', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      expect(result).toEqual(mockCurrencies);
    });

    it('should throw error when fetch fails', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
      });

      await expect(CurrencyService.getCurrencies()).rejects.toThrow(
        'Failed to fetch currencies from server'
      );
    });
  });

  describe('getCurrencyById', () => {
    it('should return currency by ID', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockCurrencies,
      });

      const result = await CurrencyService.getCurrencyById("6842f858fb5c71e0b34fd550");

      expect(result).toEqual(mockCurrencies[0]);
    });

    it('should return null for non-existent ID', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockCurrencies,
      });

      const result = await CurrencyService.getCurrencyById("non-existent");

      expect(result).toBeNull();
    });
  });

  describe('getCurrencyByName', () => {
    it('should return currency by name', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockCurrencies,
      });

      const result = await CurrencyService.getCurrencyByName("toman");

      expect(result).toEqual(mockCurrencies[0]);
    });

    it('should be case insensitive', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockCurrencies,
      });

      const result = await CurrencyService.getCurrencyByName("TOMAN");

      expect(result).toEqual(mockCurrencies[0]);
    });
  });
});

import { AuthService } from '@/services/authService';
import { API_CONFIG } from '@/config/api';

// Mock fetch globally
global.fetch = jest.fn();

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('sendOtp', () => {
    it('should send OTP successfully', async () => {
      const mockResponse = {
        success: true,
        message: 'OTP sent successfully',
        data: { expiresIn: 120 }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await AuthService.sendOtp({ mobileNumber: '09123456789' });

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining(API_CONFIG.endpoints.sendOtp),
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ mobileNumber: '09123456789' }),
        }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
      });

      await expect(
        AuthService.sendOtp({ mobileNumber: '09123456789' })
      ).rejects.toThrow('Failed to send OTP. Please try again.');
    });
  });

  describe('verifyOtp', () => {
    it('should verify OTP successfully', async () => {
      const mockResponse = {
        success: true,
        message: 'OTP verified successfully',
        data: {
          user: {
            id: '1',
            mobileNumber: '09123456789',
            isActive: true,
            lastLoginAt: '2024-01-01T00:00:00Z',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
          accessToken: 'mock-token',
          tokenType: 'Bearer'
        }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await AuthService.verifyOtp({ 
        mobileNumber: '09123456789', 
        otp: '123456' 
      });

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining(API_CONFIG.endpoints.verifyOtp),
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ mobileNumber: '09123456789', otp: '123456' }),
        }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle verification errors', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
      });

      await expect(
        AuthService.verifyOtp({ mobileNumber: '09123456789', otp: '123456' })
      ).rejects.toThrow('Failed to verify OTP. Please try again.');
    });
  });
});

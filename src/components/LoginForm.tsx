'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import styles from './LoginForm.module.css';
import { AuthService } from '@/services/authService';
import { validateMobileNumber } from '@/utils/validation';

interface LoginFormProps {
  onOtpSent?: (mobileNumber: string) => void;
}

export default function LoginForm({ onOtpSent }: LoginFormProps) {
  const [mobileNumber, setMobileNumber] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMobileNumber(value);
    
    // Clear error when user starts typing
    if (error) {
      setError('');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate mobile number
    const validation = validateMobileNumber(mobileNumber);
    if (!validation.isValid) {
      setError(validation.error || 'شماره موبایل معتبر نیست');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await AuthService.sendOtp({ mobileNumber });
      
      if (response.success) {
        // Store mobile number in sessionStorage for the next step
        sessionStorage.setItem('login_mobile', mobileNumber);
        
        // Call callback if provided
        if (onOtpSent) {
          onOtpSent(mobileNumber);
        } else {
          // Navigate to verification page
          router.push('/login/verify');
        }
      } else {
        setError(response.message || 'خطا در ارسال کد تأیید');
      }
    } catch (error) {
      console.error('Error sending OTP:', error);
      setError('خطا در ارسال کد تأیید. لطفاً دوباره تلاش کنید.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.formCard}>
        <div className={styles.header}>
          <h2 className={styles.title}>ورود به حساب کاربری</h2>
          <p className={styles.subtitle}>
            شماره موبایل خود را وارد کنید
          </p>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.inputGroup}>
            <label htmlFor="mobile" className={styles.label}>
              شماره موبایل
            </label>
            <input
              type="tel"
              id="mobile"
              value={mobileNumber}
              onChange={handleInputChange}
              placeholder="09123456789"
              className={`${styles.input} ${error ? styles.inputError : ''}`}
              disabled={isLoading}
              dir="ltr"
            />
            {error && <span className={styles.error}>{error}</span>}
          </div>

          <button
            type="submit"
            disabled={isLoading || !mobileNumber}
            className={styles.submitBtn}
          >
            {isLoading ? 'در حال ارسال...' : 'ارسال کد تأیید'}
          </button>
        </form>

        <div className={styles.footer}>
          <p className={styles.footerText}>
            با ورود به سایت، شما 
            <a href="/terms" className={styles.link}> شرایط و قوانین </a>
            را می‌پذیرید
          </p>
        </div>
      </div>
    </div>
  );
}

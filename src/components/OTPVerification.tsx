'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import styles from './OTPVerification.module.css';
import { AuthService } from '@/services/authService';
import { validateOtp, formatMobileNumber } from '@/utils/validation';
import { useAuth } from '@/contexts/AuthContext';

interface OTPVerificationProps {
  mobileNumber?: string;
  onVerificationSuccess?: () => void;
}

export default function OTPVerification({ mobileNumber: propMobileNumber, onVerificationSuccess }: OTPVerificationProps) {
  const [otp, setOtp] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [countdown, setCountdown] = useState(120); // 2 minutes countdown
  const [mobileNumber, setMobileNumber] = useState('');
  
  const router = useRouter();
  const { login } = useAuth();

  // Get mobile number from props or sessionStorage
  useEffect(() => {
    const mobile = propMobileNumber || sessionStorage.getItem('login_mobile');
    if (mobile) {
      setMobileNumber(mobile);
    } else {
      // Redirect to login if no mobile number found
      router.push('/login');
    }
  }, [propMobileNumber, router]);

  // Countdown timer
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ''); // Only allow digits
    if (value.length <= 6) {
      setOtp(value);
      
      // Clear error when user starts typing
      if (error) {
        setError('');
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate OTP
    const validation = validateOtp(otp);
    if (!validation.isValid) {
      setError(validation.error || 'کد تأیید معتبر نیست');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await AuthService.verifyOtp({ 
        mobileNumber, 
        code: otp 
      });
      
      if (response.success && response.data) {
        // Login user
        login(response.data);
        
        // Clear session storage
        sessionStorage.removeItem('login_mobile');
        
        // Call callback or redirect
        if (onVerificationSuccess) {
          onVerificationSuccess();
        } else {
          router.push('/'); // Redirect to home page
        }
      } else {
        setError(response.message || 'کد تأیید نادرست است');
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      setError('خطا در تأیید کد. لطفاً دوباره تلاش کنید.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOtp = async () => {
    if (countdown > 0 || isResending) return;

    setIsResending(true);
    setError('');

    try {
      const response = await AuthService.sendOtp({ mobileNumber });
      
      if (response.success) {
        setCountdown(120); // Reset countdown
        setOtp(''); // Clear current OTP
      } else {
        setError(response.message || 'خطا در ارسال مجدد کد');
      }
    } catch (error) {
      console.error('Error resending OTP:', error);
      setError('خطا در ارسال مجدد کد. لطفاً دوباره تلاش کنید.');
    } finally {
      setIsResending(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={styles.container}>
      <div className={styles.formCard}>
        <div className={styles.header}>
          <h2 className={styles.title}>تأیید شماره موبایل</h2>
          <p className={styles.subtitle}>
            کد تأیید ارسال شده به شماره
            <br />
            <strong>{formatMobileNumber(mobileNumber)}</strong>
            <br />
            را وارد کنید
          </p>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.inputGroup}>
            <label htmlFor="otp" className={styles.label}>
              کد تأیید
            </label>
            <input
              type="text"
              id="otp"
              value={otp}
              onChange={handleInputChange}
              placeholder="123456"
              className={`${styles.input} ${error ? styles.inputError : ''}`}
              disabled={isLoading}
              dir="ltr"
              maxLength={6}
            />
            {error && <span className={styles.error}>{error}</span>}
          </div>

          <button
            type="submit"
            disabled={isLoading || !otp || otp.length < 4}
            className={styles.submitBtn}
          >
            {isLoading ? 'در حال تأیید...' : 'تأیید و ورود'}
          </button>
        </form>

        <div className={styles.resendSection}>
          {countdown > 0 ? (
            <p className={styles.countdownText}>
              ارسال مجدد کد تا {formatTime(countdown)} دیگر
            </p>
          ) : (
            <button
              type="button"
              onClick={handleResendOtp}
              disabled={isResending}
              className={styles.resendBtn}
            >
              {isResending ? 'در حال ارسال...' : 'ارسال مجدد کد'}
            </button>
          )}
        </div>

        <div className={styles.footer}>
          <button
            type="button"
            onClick={() => router.push('/login')}
            className={styles.backBtn}
          >
            تغییر شماره موبایل
          </button>
        </div>
      </div>
    </div>
  );
}

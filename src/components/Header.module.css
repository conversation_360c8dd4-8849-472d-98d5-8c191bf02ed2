.header {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  color: #d32f2f;
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0;
}

.nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav a {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}

.nav a:hover {
  color: #d32f2f;
}

.authSection {
  display: flex;
  align-items: center;
}

.userMenu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.userInfo {
  color: #333;
  font-weight: 500;
  font-size: 0.9rem;
}

.loginBtn {
  background: #d32f2f;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.loginBtn:hover {
  background: #b71c1c;
  transform: translateY(-1px);
}

.logoutBtn {
  background: transparent;
  color: #d32f2f;
  border: 1px solid #d32f2f;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.logoutBtn:hover {
  background: #d32f2f;
  color: white;
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .nav ul {
    gap: 1rem;
  }

  .nav a {
    font-size: 0.9rem;
  }

  .logo h1 {
    font-size: 1.5rem;
  }

  .authSection {
    order: 3;
    width: 100%;
    justify-content: center;
  }

  .userMenu {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .userInfo {
    font-size: 0.8rem;
  }

  .loginBtn, .logoutBtn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}

@media (prefers-color-scheme: dark) {
  .header {
    background: #1a1a1a;
    border-bottom-color: #333;
  }

  .nav a {
    color: #e5e5e5;
  }

  .nav a:hover {
    color: #ff6b6b;
  }

  .userInfo {
    color: #e5e5e5;
  }

  .loginBtn {
    background: #ff6b6b;
  }

  .loginBtn:hover {
    background: #ff5252;
  }

  .logoutBtn {
    color: #ff6b6b;
    border-color: #ff6b6b;
  }

  .logoutBtn:hover {
    background: #ff6b6b;
    color: white;
  }
}

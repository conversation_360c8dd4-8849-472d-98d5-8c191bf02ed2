.container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem 1rem;
}

.exchangeCard {
  background: #fff;
  border: 2px solid #d32f2f;
  border-radius: 20px;
  padding: 2rem;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 10px 30px rgba(211, 47, 47, 0.1);
  position: relative;
}

.currencySection {
  margin-bottom: 1rem;
  position: relative;
}

.currencyHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.currencyInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.currencyName {
  font-weight: 600;
  color: #333;
}

.flag {
  font-size: 1.5rem;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.dropdownBtn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.8rem;
  color: #666;
  padding: 0.5rem;
  transition: transform 0.2s ease;
}

.dropdownBtn:hover {
  color: #d32f2f;
}

.dropdown {
  position: absolute;
  /* top: 100%; */
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 0.5rem;
}

.dropdownItem {
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f5f5f5;
}

.dropdownItem:last-child {
  border-bottom: none;
}

.dropdownItem:hover {
  background-color: #f8f9fa;
}

.dropdownItemActive {
  background-color: #d32f2f;
  color: white;
}

.dropdownItemActive:hover {
  background-color: #b71c1c;
}

.dropdownItemInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dropdownItemFlag {
  font-size: 1.2rem;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdownItemName {
  font-weight: 500;
  font-size: 0.9rem;
}

.amountSection {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
}

.amountInput {
  background: none;
  border: none;
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  text-align: center;
  width: 100%;
  outline: none;
  margin-bottom: 0.5rem;
}

.amountLabel {
  color: #666;
  font-size: 0.9rem;
}

.swapSection {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
}

.swapBtn {
  background: #d32f2f;
  color: white;
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.swapBtn:hover {
  background: #b71c1c;
  transform: rotate(180deg);
}

.exchangeRate {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
  margin: 1.5rem 0;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.startBtn {
  background: #07ab5e;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  width: 100%;
  transition: background 0.2s;
}

.startBtn:hover {
  background: #0f9049;
}

/* Add Estedad font to .fa-text and .amountLabel for Persian text */
.fa-text, .amountLabel.fa-text {
  font-family: 'Estedad', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

@media (max-width: 480px) {
  .container {
    padding: 1rem 0.5rem;
  }
  
  .exchangeCard {
    padding: 1.5rem;
    margin: 0 0.5rem;
  }
  
  .amountInput {
    font-size: 1.5rem;
  }
  
  .currencyName {
    font-size: 0.9rem;
  }
}

@media (prefers-color-scheme: dark) {
  .exchangeCard {
    background: #1a1a1a;
    border-color: #ff6b6b;
    color: #e5e5e5;
  }
  
  .currencyName {
    color: #e5e5e5;
  }
  
  .amountSection {
    background: #2c2c2c;
  }
  
  .amountInput {
    color: #e5e5e5;
  }
  
  .amountLabel {
    color: #ccc;
  }
  
  .exchangeRate {
    background: #2c2c2c;
    color: #ccc;
  }
  
  .flag {
    background: #2c2c2c;
  }
  
  .swapBtn {
    background: #ff6b6b;
  }
  
  .swapBtn:hover {
    background: #ff5252;
  }
  
  .startBtn {
    background: #ff6b6b;
  }
  
  .startBtn:hover {
    background: #ff5252;
  }

  .dropdown {
    background: #2c2c2c;
    border-color: #444;
  }

  .dropdownItem {
    border-bottom-color: #444;
  }

  .dropdownItem:hover {
    background-color: #3c3c3c;
  }

  .dropdownItemActive {
    background-color: #ff6b6b;
  }

  .dropdownItemActive:hover {
    background-color: #ff5252;
  }
}

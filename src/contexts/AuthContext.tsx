'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { AuthContextType, AuthData, User } from '@/types/auth';
import { storage } from '@/utils/storage';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load auth data from localStorage on mount
  useEffect(() => {
    const loadAuthData = () => {
      try {
        const authData = storage.getAuth();
        if (authData) {
          setUser(authData.user);
          setToken(authData.accessToken);
        }
      } catch (error) {
        console.error('Error loading auth data:', error);
        // Clear invalid data
        storage.removeAuth();
      } finally {
        setIsLoading(false);
      }
    };

    loadAuthData();
  }, []);

  const login = (authData: AuthData) => {
    setUser(authData.user);
    setToken(authData.accessToken);
    storage.saveAuth(authData);
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    storage.removeAuth();
  };

  const isAuthenticated = user !== null && token !== null;

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated,
    isLoading,
    login,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

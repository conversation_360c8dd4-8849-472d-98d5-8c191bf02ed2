:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary-color: #d32f2f;
  --primary-hover: #b71c1c;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary-color: #ff6b6b;
    --primary-hover: #ff5252;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  height: 100%;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

/* Use Estedad for Persian (fa) text */
:lang(fa), [lang="fa"], .fa-text, [dir="rtl"] {
  font-family: 'Estedad', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] input {
  text-align: center;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

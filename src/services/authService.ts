import { API_CONFIG, getApiUrl } from '@/config/api';
import { SendOtpRequest, SendOtpResponse, VerifyOtpRequest, VerifyOtpResponse } from '@/types/auth';

export class AuthService {
  /**
   * Send OTP to mobile number
   */
  static async sendOtp(data: SendOtpRequest): Promise<SendOtpResponse> {
    try {
      const response = await fetch(getApiUrl(API_CONFIG.endpoints.sendOtp), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: SendOtpResponse = await response.json();
      return result;
    } catch (error) {
      console.error('Error sending OTP:', error);
      throw new Error('Failed to send OTP. Please try again.');
    }
  }

  /**
   * Verify OTP and authenticate user
   */
  static async verifyOtp(data: VerifyOtpRequest): Promise<VerifyOtpResponse> {
    try {
      const response = await fetch(getApiUrl(API_CONFIG.endpoints.verifyOtp), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: VerifyOtpResponse = await response.json();
      return result;
    } catch (error) {
      console.error('Error verifying OTP:', error);
      throw new Error('Failed to verify OTP. Please try again.');
    }
  }
}

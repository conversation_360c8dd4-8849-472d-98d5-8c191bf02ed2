import { Currency } from '@/types/currency';
import { API_CONFIG, getApiUrl } from '@/config/api';

export class CurrencyService {
  /**
   * Fetch all currencies from the API
   */
  static async getCurrencies(): Promise<Currency[]> {
    try {
      const response = await fetch(getApiUrl(API_CONFIG.endpoints.currencies), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const currencies: Currency[] = await response.json();
      return currencies;
    } catch (error) {
      console.error('Error fetching currencies:', error);
      throw new Error('Failed to fetch currencies from server');
    }
  }

  /**
   * Get a specific currency by ID
   */
  static async getCurrencyById(id: string): Promise<Currency | null> {
    try {
      const currencies = await this.getCurrencies();
      return currencies.find(currency => currency._id === id) || null;
    } catch (error) {
      console.error('Error fetching currency by ID:', error);
      return null;
    }
  }

  /**
   * Get a specific currency by name
   */
  static async getCurrencyByName(name: string): Promise<Currency | null> {
    try {
      const currencies = await this.getCurrencies();
      return currencies.find(currency => currency.name.toLowerCase() === name.toLowerCase()) || null;
    } catch (error) {
      console.error('Error fetching currency by name:', error);
      return null;
    }
  }
}

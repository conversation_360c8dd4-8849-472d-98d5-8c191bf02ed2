/**
 * Validation utilities for forms
 */

/**
 * Validate Iranian mobile number
 */
export const validateMobileNumber = (mobile: string): { isValid: boolean; error?: string } => {
  // Remove any spaces or dashes
  const cleanMobile = mobile.replace(/[\s-]/g, '');
  
  // Check if it's empty
  if (!cleanMobile) {
    return { isValid: false, error: 'شماره موبایل الزامی است' };
  }
  
  // Iranian mobile number pattern (09xxxxxxxxx)
  const iranMobilePattern = /^09[0-9]{9}$/;
  
  if (!iranMobilePattern.test(cleanMobile)) {
    return { isValid: false, error: 'شماره موبایل معتبر نیست (مثال: 09123456789)' };
  }
  
  return { isValid: true };
};

/**
 * Validate OTP code
 */
export const validateOtp = (otp: string): { isValid: boolean; error?: string } => {
  // Remove any spaces
  const cleanOtp = otp.replace(/\s/g, '');
  
  // Check if it's empty
  if (!cleanOtp) {
    return { isValid: false, error: 'کد تأیید الزامی است' };
  }
  
  // OTP should be 4-6 digits
  const otpPattern = /^[0-9]{4,6}$/;
  
  if (!otpPattern.test(cleanOtp)) {
    return { isValid: false, error: 'کد تأیید باید ۴ تا ۶ رقم باشد' };
  }
  
  return { isValid: true };
};

/**
 * Format mobile number for display (add spaces for readability)
 */
export const formatMobileNumber = (mobile: string): string => {
  const cleanMobile = mobile.replace(/[\s-]/g, '');
  if (cleanMobile.length === 11) {
    return `${cleanMobile.slice(0, 4)} ${cleanMobile.slice(4, 7)} ${cleanMobile.slice(7)}`;
  }
  return mobile;
};

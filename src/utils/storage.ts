import { AuthData } from '@/types/auth';

const AUTH_STORAGE_KEY = 'sarafi_auth';

export const storage = {
  /**
   * Save authentication data to localStorage
   */
  saveAuth: (authData: AuthData): void => {
    try {
      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authData));
    } catch (error) {
      console.error('Error saving auth data:', error);
    }
  },

  /**
   * Get authentication data from localStorage
   */
  getAuth: (): AuthData | null => {
    try {
      const data = localStorage.getItem(AUTH_STORAGE_KEY);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Error getting auth data:', error);
      return null;
    }
  },

  /**
   * Remove authentication data from localStorage
   */
  removeAuth: (): void => {
    try {
      localStorage.removeItem(AUTH_STORAGE_KEY);
    } catch (error) {
      console.error('Error removing auth data:', error);
    }
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated: (): boolean => {
    const authData = storage.getAuth();
    return authData !== null && authData.accessToken !== '';
  }
};
